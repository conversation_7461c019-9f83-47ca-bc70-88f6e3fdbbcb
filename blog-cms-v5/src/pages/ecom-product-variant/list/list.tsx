import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { ListHeader } from '../../../components/list-header';
import { useGetPath, useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { extractErrorMessage } from '../../../services/api.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems, setDefaultVariant } from '../api';
import { MODULE, MODULE_POPUP } from '../config';
import { ProductVariantModal } from '../form/modal';
import { useProductVariantStore } from '../store';
import type { ProductVariant } from '../type';
import { ProductVariantSearch } from './search';

interface ProductVariantListProps {
  productId?: string;
}

function ProductVariantList({ productId }: ProductVariantListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const params = useParams();
  const routeProductId = params.productId;
  const effectiveProductId = productId || routeProductId;
  const query = queryString.parse(search);
  const { loading } = useProductVariantStore();
  const getPath = useGetPath();

  const [cursor, setCursor] = useState<string>('');
  const [hasNext, setHasNext] = useState<boolean>(false);
  const [limit] = useState<number>(10);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<ProductVariant[]>([]);
  const [idCurrent, setIdCurrent] = useState<number | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      try {
        const params = {
          ...query,
          ...filters,
          limit,
          cursor,
          ...payload,
        };

        if (effectiveProductId) {
          params.product_id = effectiveProductId;
        }

        const cleanedParams = cleanParams(params);
        const response = await getItems(cleanedParams);

        if (response.status.success) {
          setItems(response.data || []);
          setHasNext(response.meta?.has_more || false);
          setCursor(response.meta?.next_cursor || '');
        } else {
          message.error(response.status.message);
        }
      } catch (error) {
        logger('Error fetching variants', error);
        const errorMsg = extractErrorMessage(error) || t('error.fetchFailed');
        message.error(errorMsg);
      }
    },
    [filters, query, cursor, limit, effectiveProductId, logger, t],
  );

  useEffectOnce(() => {
    fetchData();
  });

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    setCursor('');
  };

  const handleDelete = async (id: number) => {
    try {
      const res = await deleteItem(String(id));
      if (res.status.success) {
        message.success(t('deleteSuccess'));
        fetchData();
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      const errorMsg = extractErrorMessage(error) || t('error.deleteFailed');
      message.error(errorMsg);
    }
  };

  const handleSetDefault = async (id: number) => {
    try {
      const res = await setDefaultVariant(String(id));
      if (res.status.success) {
        message.success(t('updateSuccess'));
        fetchData();
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      const errorMsg = extractErrorMessage(error) || t('error.updateFailed');
      message.error(errorMsg);
    }
  };

  const handleActions = (action: string, record?: ProductVariant) => {
    if (action === 'edit' && record) {
      if (MODULE_POPUP) {
        setIdCurrent(record.variant_id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.variant_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        if (effectiveProductId) {
          navigate(getPath(`/${MODULE}/create?product_id=${effectiveProductId}`));
        } else {
          navigate(`/${MODULE}/create`);
        }
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handleNextPage = () => {
    if (hasNext && cursor) {
      fetchData({ cursor });
    }
  };

  const handlePrevPage = () => {
    setCursor('');
    fetchData({ cursor: '' });
  };

  const columns = [
    {
      title: t('variantId'),
      dataIndex: 'variant_id',
      key: 'variant_id',
      width: 80,
    },
    {
      title: t('product'),
      dataIndex: 'product',
      key: 'product',
      render: (product: any) => product?.name || '-',
    },
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('sku'),
      dataIndex: 'sku',
      key: 'sku',
    },
    {
      title: t('price'),
      dataIndex: 'price',
      key: 'price',
      render: (price: number | undefined) =>
        price !== undefined && price !== null ? `$${price.toFixed(2)}` : '-',
    },
    {
      title: t('stockQuantity'),
      dataIndex: 'stock_quantity',
      key: 'stock_quantity',
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap: Record<string, string> = {
          active: 'green',
          draft: 'orange',
          archived: 'red',
        };
        return (
          <Tag color={colorMap[status] || 'default'}>
            {t(`status${status.charAt(0).toUpperCase() + status.slice(1)}`)}
          </Tag>
        );
      },
    },
    {
      title: t('isDefault'),
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault: boolean) =>
        isDefault ? <Tag color="green">{t('yes')}</Tag> : <Tag>{t('no')}</Tag>,
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: ProductVariant) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'setDefault',
                label: t('btnSetDefault'),
                disabled: record.is_default,
                onClick: () => handleSetDefault(record.variant_id),
              },
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.variant_id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <ListHeader
        title={
          effectiveProductId
            ? `${t('module')} - Product ${effectiveProductId}`
            : t('module')
        }
        module={MODULE}
        onAddClick={() => handleActions('add')}
        addButtonText={t('btnAdd')}
        showBackButton={true}
        backDestination="dashboard"
      />

      {!effectiveProductId && (
        <div className="p-4 bg-white shadow">
          <ProductVariantSearch onSearch={handleFilters} />
        </div>
      )}

      <Space direction="vertical" className="bg-white w-full gap-0 mt-4">
        <Table
          columns={columns}
          dataSource={items}
          rowKey="variant_id"
          loading={loading}
          pagination={false}
        />
        <div className="flex justify-end gap-2 p-4">
          <Button onClick={handlePrevPage} disabled={!cursor}>
            {t('btnPrevious')}
          </Button>
          <Button onClick={handleNextPage} disabled={!hasNext}>
            {t('btnNext')}
          </Button>
        </div>
      </Space>

      {showModal && (
        <ProductVariantModal
          open={showModal}
          id={idCurrent}
          productId={effectiveProductId}
          onClose={handleModal}
        />
      )}
    </div>
  );
}

export default ProductVariantList;
