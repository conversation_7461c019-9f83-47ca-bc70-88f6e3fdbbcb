import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  ReloadOutlined,
  SearchOutlined,
  SortAscendingOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { BackButton, ButtonLink } from '../../../components/button';
import { ListHeader } from '../../../components/list-header';
import { StatusTag } from '../../../components/table';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  getPageNumber,
} from '../../../services/utils.service';
import { deleteItem, getHierarchy } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useEcomCategoryStore from '../store';
import { EcomCategoryResponse } from '../type';
import { SEOModal } from '../components';
import Search from './search';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading, rebuildHierarchy } = useEcomCategoryStore();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showSortModal, setShowSortModal] = useState(false);
  const [items, setItems] = useState<EcomCategoryResponse[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [showSEOModal, setShowSEOModal] = useState(false);
  const [selectedCategory, setSelectedCategory] =
    useState<EcomCategoryResponse | null>(null);

  async function fetchData(payload?: any) {
    const params = {
      ...pagination,
      ...query,
      ...filters,
      ...payload,
    };
    //changeBrowserLocation(navigate, pathname, params);
    const response = await getHierarchy(params);
    if (response.status.success) {
      setItems(response.data || []);
      // Update total for pagination if needed
      // setTotal(response.meta.total);
    } else {
      message.error(response.status.message);
    }
  }

  const onPagingChange = (page: number, limit: number) => {
    logger('[page]', { page, limit });
    setPagination({ page, limit });
    fetchData({ page, limit });
  };

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData(values);
  };

  useEffectOnce(() => {
    fetchData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleRebuild = async () => {
    try {
      await rebuildHierarchy();
      message.success(t('rebuildSuccess', 'Hierarchy rebuilt successfully'));
      fetchData(); // Refresh data after rebuild
    } catch (error) {
      message.error(t('rebuildError', 'Failed to rebuild hierarchy'));
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    } else if (action === 'sort') {
      navigate(`/${MODULE}/tree`);
    } else if (action === 'rebuild') {
      handleRebuild();
    } else if (action === 'seo') {
      setSelectedCategory(record);
      setShowSEOModal(true);
    }
  };

  const handleTableChange = (page: number, pageSize: number) => {
    onPagingChange(page, pageSize);
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handleSortModalClose = () => {
    setShowSortModal(false);
    fetchData(); // Refresh data after sorting
  };

  const renderCategoryTree = (categories: EcomCategoryResponse[]) => {
    return categories.map((category) => {
      const hasChildren = category.children && category.children.length > 0;

      return (
        <React.Fragment key={category.id}>
          <tr>
            <td>{category.id}</td>
            <td>{category.name}</td>
            <td>{category.slug}</td>
            <td>
              <StatusTag
                value={category.status}
                statusList={[
                  { value: 'active', label: 'Hoạt động', color: 'green' },
                  { value: 'inactive', label: 'Không hoạt động', color: 'red' },
                  { value: 'deleted', label: 'Đã xóa', color: 'gray' },
                ]}
              />
            </td>
            <td>
              <Dropdown.Button
                menu={{
                  items: [
                    {
                      key: 'seo',
                      label: (
                        <span onClick={() => handleActions('seo', category)}>
                          <SearchOutlined /> SEO Settings
                        </span>
                      ),
                    },
                    {
                      key: 'delete',
                      label: (
                        <Popconfirm
                          placement="top"
                          title={t('deleteConfirm')}
                          onConfirm={() => handleDelete(category.id.toString())}
                          okText="Yes"
                          cancelText="No"
                        >
                          <DeleteOutlined /> {t('btnDelete')}
                        </Popconfirm>
                      ),
                    },
                  ],
                }}
                onClick={() => handleActions('edit', category)}
              >
                <EditOutlined /> {t('btnEdit')}
              </Dropdown.Button>
            </td>
          </tr>
          {hasChildren &&
            category.children &&
            renderCategoryTree(category.children)}
        </React.Fragment>
      );
    });
  };

  const columns = [
    {
      title: t('id'),
      dataIndex: 'id',
      key: 'id',
      render: (dom: any, record: any) => (
        <span
          className="text-blue-600 cursor-pointer"
          onClick={() => navigate(`/${MODULE}/${record.id}`)}
        >
          {dom}
        </span>
      ),
    },
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('slug'),
      dataIndex: 'slug',
      key: 'slug',
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (value: string) => (
        <StatusTag
          value={value}
          statusList={[
            { value: 'active', label: 'Bật', color: 'green' },
            { value: 'inactive', label: 'Tắt', color: 'red' },
            { value: 'deleted', label: 'Đã xóa', color: 'gray' },
          ]}
        />
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'seo',
                label: (
                  <span onClick={() => handleActions('seo', record)}>
                    <SearchOutlined /> SEO Settings
                  </span>
                ),
              },
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.id.toString())}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <ListHeader
        title={t('module')}
        module={MODULE}
        onAddClick={() => navigate(`/${MODULE}/create`)}
        addButtonText={t('btnAdd')}
        showBackButton={true}
        backDestination="dashboard"
        customActions={
          <>
            <Button
              type="default"
              icon={<ReloadOutlined />}
              onClick={() => handleActions('rebuild', null)}
              loading={loading}
            >
              {t('btnRebuild', 'Rebuild')}
            </Button>
            <Button
              type="primary"
              icon={<SortAscendingOutlined />}
              onClick={() => handleActions('sort', null)}
            >
              {t('btnSort')}
            </Button>
          </>
        }
      />
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <Pagination
              defaultCurrent={getPageNumber(query, 'page', 1)}
              total={total}
              defaultPageSize={pagination.limit}
              showSizeChanger
              showTitle={false}
              onChange={handleTableChange}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
        <SEOModal
          visible={showSEOModal}
          category={selectedCategory}
          onClose={() => {
            setShowSEOModal(false);
            setSelectedCategory(null);
          }}
        />
      </Space>
    </div>
  );
}
