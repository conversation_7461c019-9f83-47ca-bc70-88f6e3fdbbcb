import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { ListHeader } from '../../../components/list-header';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  cleanParams,
} from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { AttributeGroupModal } from '../form/modal';
import useAttributeGroupStore from '../store';
import { ProductAttributeGroup } from '../type';
import { AttributeGroupSearch } from './search';

// Using type instead of interface to avoid ESLint empty interface warning
type AttributeGroupListProps = Record<string, never>;

function AttributeGroupList(_props: AttributeGroupListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useAttributeGroupStore();

  const [cursor, setCursor] = useState<string>('');
  const [hasNext, setHasNext] = useState<boolean>(false);
  const [limit] = useState<number>(10);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<ProductAttributeGroup[]>([]);
  const [idCurrent, setIdCurrent] = useState<number | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        limit,
        cursor,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data || []);
        setHasNext(response.meta?.has_next || false);
        setCursor(response.meta?.next_cursor || '');
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname, cursor, limit],
  );

  useEffectOnce(() => {
    fetchData();
  });

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    setCursor(''); // Reset cursor when filtering
    fetchData({ ...values, cursor: '' });
  };

  const handleDelete = async (id: number) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.group_id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.group_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handleNextPage = () => {
    if (hasNext && cursor) {
      fetchData({ cursor });
    }
  };

  const handlePrevPage = () => {
    setCursor('');
    fetchData({ cursor: '' });
  };

  const columns = [
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('code'),
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: t('displayOrder'),
      dataIndex: 'display_order',
      key: 'display_order',
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.group_id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <ListHeader
        title={t('module')}
        module={MODULE}
        onAddClick={() => handleActions('add', null)}
        addButtonText={t('btnAdd')}
        showBackButton={true}
        backDestination="dashboard"
      />
      <AttributeGroupSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></AttributeGroupSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="group_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        />
        <div className="flex justify-end gap-2 p-4">
          <Button onClick={handlePrevPage} disabled={!cursor}>
            {t('btnPrevious')}
          </Button>
          <Button onClick={handleNextPage} disabled={!hasNext}>
            {t('btnNext')}
          </Button>
        </div>
      </Space>

      {showModal && (
        <AttributeGroupModal
          id={idCurrent}
          showModal={showModal}
          onChange={handleModal}
        ></AttributeGroupModal>
      )}
    </div>
  );
}

export { AttributeGroupList };
