import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Spin,
  Switch,
} from 'antd';
import _ from 'lodash';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { extractErrorMessage } from '../../../services/api.service';
import { AxiosError } from 'axios';
import { SelectEcomCategoryTreeCheckbox } from '../../ecom-category/components/select-ecom-category-tree-checkbox';
import {
  createItem,
  getItem,
  getProductCategories,
  getProductCategoriesById,
  updateItem,
  bulkUpdateProductCategories,
  getAttributes
} from '../api';
import {
  ConfigurableAttributesSelector,
  ProductAttribute,
  ProductAttributes,
  ProductOptions,
  ProductVariant,
  ProductVariants,
  SelectedConfigurableAttribute,
  SelectedProductAttribute,
  SelectedProductOption,
} from '../components';
import { MODULE } from '../config';
import { Product, ProductStatus, ProductType } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

interface ProductFormValues extends Product {
  attributes?: ProductAttribute[];
  variants?: ProductVariant[];
  product_options?: SelectedProductOption[];
}

interface ProductCategory {
  category_id: number;
  name: string;
  description?: string;
  parent_id?: number;
  level?: number;
}

interface AttributeMetadata {
  attribute_id: number;
  name: string;
  code: string;
  type?: string;
  input_type?: string;
  is_required: boolean;
}

interface CategoryData {
  category_id: number;
  name: string;
  parent_id?: number;
}

const ProductForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [productType, setProductType] = useState<string>(ProductType.SIMPLE);
    const [variantsModalVisible, setVariantsModalVisible] =
      useState<boolean>(false);
    const [productAttributes, setProductAttributes] = useState<
      ProductAttribute[]
    >([]);
    const [productVariants, setProductVariants] = useState<ProductVariant[]>(
      [],
    );
    const [selectedAttributes, setSelectedAttributes] = useState<
      SelectedProductAttribute[]
    >([]);
    const [configurableAttributes, setConfigurableAttributes] = useState<
      SelectedConfigurableAttribute[]
    >([]);
    const [manageVariantPricing, setManageVariantPricing] =
      useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);

    // Use useMemo to prevent unnecessary re-renders
    const initForm = useMemo(
      () => ({
        status: ProductStatus.DRAFT,
        product_type: ProductType.SIMPLE,
        price: 0,
        is_taxable: false,
        is_virtual: false,
        is_downloadable: false,
        attributes: [],
        variants: [],
      }),
      [],
    );

    // Use useCallback to prevent unnecessary re-renders
    const fetchCategories = useCallback(async () => {
      try {
        setLoading(true);
        const res = await getProductCategories();
        if (res.status.success) {
          // Categories are loaded but not stored in state since they're not used
          logger('Categories loaded successfully');
        }
      } catch (error) {
        logger('Error fetching categories', error);
        const errorMsg =
          extractErrorMessage(error as AxiosError) ||
          t('form.fetchCategoriesError');
        message.error(errorMsg);
      } finally {
        setLoading(false);
      }
    }, [logger, t]);

    // Use useCallback to prevent unnecessary re-renders
    const getItemData = useCallback(
      async (_id: string) => {
        try {
          setLoading(true);
          const res = await getItem(_id);
          if (res.status.success) {
            const productData = res.data;
            // Nếu có attributes trong response, set vào state
            if (productData.attributes) {
              setProductAttributes(productData.attributes);
              setManageVariantPricing(!!productData.manage_variant_pricing);
            }
            // Nếu có variants trong response, set vào state
            if (productData.variants) {
              setProductVariants(productData.variants);
            }
            // Load product attribute values from product response
            if (productData.product_attribute_values && productData.product_attribute_values.length > 0) {
              try {
                // Fetch all attributes to get their metadata
                const attributesRes = await getAttributes({ limit: 100 });

                const attributesMap = new Map<number, Partial<AttributeMetadata>>();
                if (attributesRes.status.success && attributesRes.data) {
                  attributesRes.data.forEach((attr: AttributeMetadata) => {
                    attributesMap.set(attr.attribute_id, {
                      name: attr.name,
                      code: attr.code,
                      type: attr.input_type || attr.type,
                      is_required: attr.is_required
                    });
                  });
                }

                // Map the loaded values to the format expected by ProductAttributes component
                const attrs: SelectedProductAttribute[] =
                  productData.product_attribute_values.map((val: any) => {
                    const attrMeta = attributesMap.get(val.attribute_id) || {};

                    // Get the correct value based on attribute type
                    let value =
                      val.value_text ||
                      val.value_numeric ||
                      val.value_date ||
                      (val.value_boolean !== null &&
                      val.value_boolean !== undefined
                        ? val.value_boolean
                        : null) ||
                      val.value ||
                      '';

                    const mappedAttr = {
                      attribute_id: val.attribute_id,
                      attribute_name: attrMeta.name || '',
                      attribute_code: attrMeta.code || '',
                      attribute_type: attrMeta.type || 'text',
                      is_required: attrMeta.is_required || false,
                      value: value,
                      option_id: val.attribute_option_id || val.option_id,
                    };
                    return mappedAttr;
                  });

                setSelectedAttributes(attrs);
                logger(
                  'Loaded product attribute values from product data:',
                  attrs,
                );
                logger(
                  'Product attribute values raw data:',
                  productData.product_attribute_values,
                );
                logger('Attributes metadata map:', attributesMap);

                // Also set the form field value
                form.setFieldsValue({ product_attribute_values: attrs });
              } catch (error) {
                logger('Error processing product attribute values:', error);
                const errorMsg =
                  extractErrorMessage(error as AxiosError) ||
                  t('form.fetchAttributeValuesError');
                message.warning(errorMsg);
              }
            }

            // Load product categories
            if (productData.id) {
              const categoriesRes = await getProductCategoriesById(
                Number(productData.id),
              );
              if (categoriesRes.status.success && categoriesRes.data) {
                const categoryIds = categoriesRes.data.map(
                  (cat: CategoryData) => cat.category_id,
                );
                form.setFieldsValue({ category_ids: categoryIds });
              }
            }

            // Map backend fields to frontend form fields
            const mappedData = {
              ...productData,
              // Map backend featured_image to frontend image_url for backward compatibility
              image_url: (productData as any).featured_image,
              // Map backend description to content for form display
              content: productData.description,
            };

            form.setFieldsValue(mappedData);
            if (productData.product_type) {
              setProductType(productData.product_type);
            }
          } else {
            message.error(res.status.message);
          }
        } catch (error) {
          logger('Error fetching product data', error);
          const errorMsg =
            extractErrorMessage(error as AxiosError) || t('form.fetchError');
          message.error(errorMsg);
        } finally {
          setLoading(false);
        }
      },
      [form, logger, t],
    );

    useEffect(() => {
      fetchCategories();
    }, [fetchCategories]);

    useEffect(() => {
      logger('Product ID:', id);
      form.resetFields();
      // Check for undefined, 'undefined' string, 'create', or invalid IDs
      if (['create', undefined, 'undefined', null].includes(id) || !id) {
        setIsNew(true);
        form.setFieldsValue(initForm);
      } else if (id && id !== 'undefined') {
        setIsNew(false);
        getItemData(id);
      }
    }, [id, form, getItemData, initForm, logger]);

    const onFinish = async (values: ProductFormValues) => {
      try {
        setLoading(true);
        // Transform product_attribute_values to the format API expects
        let attributeValues = values.product_attribute_values;
        if (attributeValues && Array.isArray(attributeValues)) {
          attributeValues = attributeValues.map(
            (attr: SelectedProductAttribute) => {
              const baseValue = {
                attribute_id: attr.attribute_id,
                attribute_option_id: attr.option_id || null,
              };

              // Map value to correct field based on attribute type
              if (
                attr.attribute_type === 'price' ||
                attr.attribute_type === 'number'
              ) {
                return {
                  ...baseValue,
                  value_numeric: Number(attr.value) || null,
                };
              } else if (attr.attribute_type === 'date') {
                return { ...baseValue, value_date: attr.value || null };
              } else if (
                attr.attribute_type === 'boolean' ||
                attr.attribute_type === 'checkbox'
              ) {
                return { ...baseValue, value_boolean: Boolean(attr.value) };
              } else {
                // Default to text for text, textarea, select, multiselect, etc.
                return { ...baseValue, value_text: String(attr.value || '') };
              }
            },
          );
        }

        // Map frontend fields to backend DTO fields
        const productData: Record<string, any> = {
          ...values,
          // Map image_url to featured_image (backend field name)
          featured_image: values.image_url,
          // Map content to description (backend expects description, not content)
          description: values.content || values.description,
          // short_description can be derived from description if needed
          short_description:
            values.description || values.content?.substring(0, 255),
          manage_variant_pricing: manageVariantPricing,
          variants: productVariants,
          product_attribute_values: attributeValues,
        };

        // Remove frontend-only fields that backend doesn't expect
        delete productData.image_url;
        delete productData.content;
        // Note: product_type, is_taxable, is_virtual, is_downloadable might be frontend-only
        // Keep them for now as they might be handled by backend even if not in DTO

        let res;
        if (isNew) {
          res = await createItem(productData);
          if (res.status.success) {
            message.success(t('form.addSuccess'));

            // Save categories for new product
            if (
              values.category_ids &&
              values.category_ids.length > 0 &&
              res.data.product_id
            ) {
              await bulkUpdateProductCategories(
                res.data.product_id,
                values.category_ids,
                undefined,
                'replace',
              );
            }

            // Note: Product attribute values are now handled by the backend
            // when included in the product_attribute_values field of productData
          }
        } else {
          res = await updateItem(id!, productData);
          if (res.status.success) {
            message.success(t('form.updateSuccess'));

            // Update categories for existing product - validate ID first
            if (values.category_ids && id && id !== 'undefined') {
              const productId = Number(id);
              if (!isNaN(productId) && productId > 0) {
                await bulkUpdateProductCategories(
                  productId,
                  values.category_ids,
                  undefined,
                  'replace',
                );
              } else {
                logger('Warning: Invalid product ID for category update:', id);
              }
            }

            // Note: Product attribute values are now handled by the backend
            // when included in the product_attribute_values field of productData
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        const errorMsg =
          extractErrorMessage(error as AxiosError) ||
          (isNew ? t('form.addError') : t('form.updateError'));
        message.error(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    const handleValuesChange = (
      changedValues: Partial<ProductFormValues>,
      allValues: ProductFormValues,
    ) => {
      logger(changedValues);
      logger(allValues);

      // Update product type state when it changes
      if (changedValues.product_type) {
        setProductType(changedValues.product_type);
      }

      // Update attributes state when attributes change
      if (changedValues.attributes) {
        setProductAttributes(changedValues.attributes);
      }
    };

    const handleVariantsChange = (variants: ProductVariant[]) => {
      setProductVariants(variants);
    };

    const handleSelectedAttributesChange = (
      attrs: SelectedProductAttribute[],
    ) => {
      setSelectedAttributes(attrs);
      form.setFieldsValue({ product_attribute_values: attrs });
    };

    const handleConfigurableAttributesChange = (
      attrs: SelectedConfigurableAttribute[],
    ) => {
      setConfigurableAttributes(attrs);
      form.setFieldsValue({ configurable_attributes: attrs });
    };

    const showVariantsModal = () => {
      setVariantsModalVisible(true);
    };

    const hideVariantsModal = () => {
      setVariantsModalVisible(false);
    };

    const handleVariantsApply = () => {
      // form.setFieldsValue({ variants: productVariants });
      setVariantsModalVisible(false);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    // Chỉ hiển thị Configurable Attributes nếu sản phẩm là loại CONFIGURABLE
    const showProductOptions = productType === ProductType.CONFIGURABLE;

    // Chỉ hiển thị nút quản lý biến thể khi có ít nhất một configurable attribute với options
    const canManageVariants =
      showProductOptions &&
      configurableAttributes &&
      configurableAttributes.length > 0 &&
      configurableAttributes.every(
        (attr) =>
          attr.selected_option_ids && attr.selected_option_ids.length > 0,
      );

    return (
      <Spin spinning={loading} tip={t('form.loading')}>
        <Form
          form={form}
          name="form"
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
          initialValues={initForm}
          onValuesChange={handleValuesChange}
        >
          <div className="form_content">
            <Row gutter={16}>
              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.name')}
                  name="name"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <Input />
                </FormItem>
              </Col>
              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.slug')}
                  name="slug"
                  tooltip={t('form.slugTooltip')}
                >
                  <Input />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.sku')}
                  name="sku"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <Input />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.categories')}
                  name="category_ids"
                  tooltip={t('form.categoriesHelp')}
                >
                  <SelectEcomCategoryTreeCheckbox
                    multiple
                    placeholder={t('form.selectCategories')}
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.productType')}
                  name="product_type"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <Select
                    options={Object.entries(ProductType).map(([_, label]) => ({
                      value: label,
                      label: t(`productType.${label}`),
                    }))}
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.price')}
                  name="price"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    prefix="$"
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('form.costPrice')} name="cost_price">
                  <InputNumber
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    prefix="$"
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.status')}
                  name="status"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <Select
                    options={Object.entries(ProductStatus).map(
                      ([_, label]) => ({
                        value: label,
                        label: t(`status.${label}`),
                      }),
                    )}
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('form.featuredImage')} name="image_url">
                  <Input placeholder={t('form.featuredImagePlaceholder')} />
                </FormItem>
              </Col>

              <Col xs={24} lg={8}>
                <FormItem
                  label={t('form.isTaxable')}
                  name="is_taxable"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>

              <Col xs={24} lg={8}>
                <FormItem
                  label={t('form.isVirtual')}
                  name="is_virtual"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>

              <Col xs={24} lg={8}>
                <FormItem
                  label={t('form.isDownloadable')}
                  name="is_downloadable"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>

              <Col xs={24}>
                <FormItem label={t('form.description')} name="description">
                  <Input.TextArea rows={4} />
                </FormItem>
              </Col>

              <Col xs={24}>
                <FormItem label={t('form.content')} name="content">
                  <Input.TextArea rows={8} />
                </FormItem>
              </Col>
            </Row>

            <Divider orientation="left">
              {t('productAttributes.sectionTitle') || 'Product Attributes'}
            </Divider>
            <FormItem name="product_attribute_values" noStyle>
              <ProductAttributes
                productId={id ? Number(id) : undefined}
                value={selectedAttributes}
                onChange={handleSelectedAttributesChange}
              />
            </FormItem>

            {showProductOptions && (
              <>
                <Divider orientation="left">
                  {t('configurableAttributes.sectionTitle') ||
                    'Configurable Attributes'}
                </Divider>
                <FormItem name="configurable_attributes" noStyle>
                  <ConfigurableAttributesSelector
                    value={configurableAttributes}
                    onChange={handleConfigurableAttributesChange}
                  />
                </FormItem>

                {canManageVariants && (
                  <div style={{ marginTop: 16, textAlign: 'right' }}>
                    <Button type="primary" onClick={showVariantsModal}>
                      {t('productVariants.manageVariants') || 'Manage Variants'}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Modal quản lý biến thể sản phẩm */}
          <ProductVariants
            visible={variantsModalVisible}
            onCancel={hideVariantsModal}
            onApply={handleVariantsApply}
            attributes={productAttributes}
            value={productVariants}
            onChange={handleVariantsChange}
            basePrice={form.getFieldValue('price') || 0}
          />
        </Form>
      </Spin>
    );
  },
);
ProductForm.displayName = 'ProductForm';

export { ProductForm };
