import { DeleteOutlined, PlusOutlined, PlusCircleOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Space,
  Table,
  Typography,
  message,
} from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { extractErrorMessage } from '../../../services/api.service';
import {
  getAttributes,
  getAttributeOptionsByAttributeId,
  createAttributeOption,
} from '../api';
import { MODULE } from '../config';
import {
  AttributeType,
  ProductAttribute,
  ProductAttributeOption,
  ProductAttributeValue,
} from '../type';

const { Text } = Typography;

// Interface for selected attribute with value
interface SelectedProductAttribute {
  attribute_id: number;
  attribute_name: string;
  attribute_code: string;
  attribute_type: string;
  value?: string | number | boolean;
  option_id?: number;
  is_required: boolean;
}

interface ProductAttributesProps {
  productId?: number;
  value?: SelectedProductAttribute[];
  onChange?: (value: SelectedProductAttribute[]) => void;
}

const ProductAttributes: React.FC<ProductAttributesProps> = ({
  productId,
  value = [],
  onChange,
}) => {
  const { t } = useTranslation(MODULE);
  const [loading, setLoading] = useState<boolean>(false);
  const [availableAttributes, setAvailableAttributes] = useState<
    ProductAttribute[]
  >([]);
  // Remove local selectedAttributes state - use value prop directly
  const [attributeOptions, setAttributeOptions] = useState<
    Record<number, ProductAttributeOption[]>
  >({});
  const [isInitialized, setIsInitialized] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalAttributeId, setModalAttributeId] = useState<number | null>(null);
  const [modalAttributeName, setModalAttributeName] = useState<string>('');
  const [modalLoading, setModalLoading] = useState(false);
  const [form] = Form.useForm();

  // Use ref to track fetched options
  const optionsFetchedRef = useRef<Set<number>>(new Set());

  // Fetch all available attributes
  const fetchAttributes = useCallback(async () => {
    try {
      setLoading(true);
      // Use regular getAttributes with limit parameter instead of /all endpoint
      const response = await getAttributes({ limit: 100 });
      if (response.status.success && response.data) {
        // Map API response to match frontend type and set default is_active = true
        const mappedAttributes = response.data.map((attr: any) => ({
          ...attr,
          type: attr.input_type || attr.type, // Map input_type to type
          is_active: attr.is_active !== undefined ? attr.is_active : true // Default to true if not present
        }));
        setAvailableAttributes(mappedAttributes.filter((attr) => attr.is_active));
      }
    } catch (error) {
      const errorMsg =
        extractErrorMessage(error) || t('error.fetchAttributesFailed');
      message.error(errorMsg);
    } finally {
      setLoading(false);
      setIsInitialized(true);
    }
  }, [t]);

  // Fetch options for select-type attributes - Remove t from dependencies to prevent recreation
  const fetchAttributeOptions = useCallback(async (attributeId: number) => {
    try {
      const response = await getAttributeOptionsByAttributeId(
        attributeId.toString(),
      );
      if (response.status.success && response.data) {
        // Map options with default is_active = true if not present
        const mappedOptions = response.data.map((opt: any) => ({
          ...opt,
          is_active: opt.is_active !== undefined ? opt.is_active : true
        }));
        setAttributeOptions((prev) => ({
          ...prev,
          [attributeId]: mappedOptions.filter((opt) => opt.is_active),
        }));
      }
    } catch (error) {
      // Don't use t() here to avoid dependency
      const errorMsg = extractErrorMessage(error) || 'Failed to fetch attribute options';
      message.warning(errorMsg);
    }
  }, []);

  // Initialize attributes once
  useEffect(() => {
    if (!isInitialized) {
      fetchAttributes();
    }
  }, [isInitialized, fetchAttributes]);

  // Fetch options for select-type attributes when value changes
  useEffect(() => {
    if (!value || value.length === 0) return;

    // Fetch options for select-type attributes that we haven't fetched yet
    value.forEach((attr) => {
      if (
        (attr.attribute_type === AttributeType.SELECT ||
         attr.attribute_type === AttributeType.MULTISELECT) &&
        !optionsFetchedRef.current.has(attr.attribute_id)
      ) {
        optionsFetchedRef.current.add(attr.attribute_id);
        fetchAttributeOptions(attr.attribute_id);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(value.map(v => v.attribute_id))]); // Only re-run when attribute IDs change

  const handleAddAttribute = () => {
    // Check if there are any available attributes first
    if (availableAttributes.length === 0) {
      message.warning(t('noAttributesAvailable') || 'No attributes available in the system');
      return;
    }

    // Find attributes not yet selected
    const unselectedAttributes = availableAttributes.filter(
      (attr) =>
        !value.some((sel) => sel.attribute_id === attr.attribute_id),
    );

    if (unselectedAttributes.length === 0) {
      message.warning(t('allAttributesAdded') || 'All attributes already added');
      return;
    }

    // Add first unselected attribute
    const firstAttr = unselectedAttributes[0];
    const newAttr: SelectedProductAttribute = {
      attribute_id: firstAttr.attribute_id,
      attribute_name: firstAttr.name,
      attribute_code: firstAttr.code,
      attribute_type: firstAttr.type,
      is_required: firstAttr.is_required,
      value: undefined,
    };

    // Fetch options if it's a select type
    if (
      firstAttr.type === AttributeType.SELECT ||
      firstAttr.type === AttributeType.MULTISELECT
    ) {
      optionsFetchedRef.current.add(firstAttr.attribute_id);
      fetchAttributeOptions(firstAttr.attribute_id);
    }

    const updated = [...value, newAttr];
    onChange?.(updated);
  };

  const handleRemoveAttribute = (attributeId: number) => {
    const updated = value.filter(
      (attr) => attr.attribute_id !== attributeId,
    );
    onChange?.(updated);
  };

  const handleAttributeChange = (index: number, attributeId: number) => {
    const attribute = availableAttributes.find(
      (attr) => attr.attribute_id === attributeId,
    );
    if (!attribute) return;

    // Fetch options if it's a select type
    if (
      attribute.type === AttributeType.SELECT ||
      attribute.type === AttributeType.MULTISELECT
    ) {
      if (!optionsFetchedRef.current.has(attribute.attribute_id)) {
        optionsFetchedRef.current.add(attribute.attribute_id);
        fetchAttributeOptions(attribute.attribute_id);
      }
    }

    const updated = [...value];
    updated[index] = {
      ...updated[index],
      attribute_id: attribute.attribute_id,
      attribute_name: attribute.name,
      attribute_code: attribute.code,
      attribute_type: attribute.type,
      is_required: attribute.is_required,
      value: undefined,
      option_id: undefined,
    };
    onChange?.(updated);
  };

  const handleValueChange = (
    index: number,
    val: any,
    optionId?: number,
  ) => {
    const updated = [...value];
    updated[index] = {
      ...updated[index],
      value: val,
      option_id: optionId,
    };
    onChange?.(updated);
  };

  const handleOpenAddOptionModal = (attributeId: number, attributeName: string) => {
    setModalAttributeId(attributeId);
    setModalAttributeName(attributeName);
    setModalVisible(true);
    form.resetFields();
  };

  const handleCloseModal = () => {
    setModalVisible(false);
    setModalAttributeId(null);
    setModalAttributeName('');
    form.resetFields();
  };

  const handleCreateOption = async () => {
    try {
      const values = await form.validateFields();
      setModalLoading(true);

      if (!modalAttributeId) {
        message.error(t('error.invalidAttribute') || 'Invalid attribute');
        return;
      }

      const payload = {
        attribute_id: modalAttributeId,
        value: values.value,
        label: values.label,
        display_order: values.display_order || 99,
      };

      const response = await createAttributeOption(payload);

      if (response.status.success) {
        message.success(t('addOptionSuccess') || 'Option added successfully');

        // Refresh the options for this attribute
        await fetchAttributeOptions(modalAttributeId);

        // Find the newly created option and update the selected attribute's value
        const newOption = response.data;
        const attrIndex = value.findIndex(
          attr => attr.attribute_id === modalAttributeId
        );

        if (attrIndex !== -1) {
          handleValueChange(attrIndex, newOption.value, newOption.option_id);
        }

        handleCloseModal();
      } else {
        const errorMsg = extractErrorMessage(response) || t('error.createOptionFailed');
        message.error(errorMsg);
      }
    } catch (error) {
      const errorMsg = extractErrorMessage(error) || t('error.createOptionFailed');
      message.error(errorMsg);
    } finally {
      setModalLoading(false);
    }
  };

  const renderValueInput = (attr: SelectedProductAttribute, index: number) => {
    const options = attributeOptions[attr.attribute_id] || [];

    switch (attr.attribute_type) {
      case AttributeType.SELECT:
        return (
          <Space.Compact style={{ width: '100%' }}>
            <Select
              style={{ width: '100%' }}
              placeholder={t('selectValue') || 'Select value'}
              value={attr.option_id}
              onChange={(optionId) => {
                const option = options.find((opt) => opt.option_id === optionId);
                handleValueChange(index, option?.value, optionId);
              }}
              options={options.map((opt) => ({
                label: opt.label || opt.value,
                value: opt.option_id,
              }))}
            />
            <Button
              icon={<PlusCircleOutlined />}
              onClick={() => handleOpenAddOptionModal(attr.attribute_id, attr.attribute_name)}
              title={t('addOption') || 'Add Option'}
            />
          </Space.Compact>
        );

      case AttributeType.MULTISELECT:
        return (
          <Space.Compact style={{ width: '100%' }}>
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              placeholder={t('selectValues') || 'Select values'}
              value={
                typeof attr.value === 'string'
                  ? attr.value.split(',').map(Number)
                  : []
              }
              onChange={(optionIds) => {
                const selectedOptions = options.filter((opt) =>
                  optionIds.includes(opt.option_id),
                );
                const values = selectedOptions.map((opt) => opt.value).join(',');
                handleValueChange(index, values);
              }}
              options={options.map((opt) => ({
                label: opt.label || opt.value,
                value: opt.option_id,
              }))}
            />
            <Button
              icon={<PlusCircleOutlined />}
              onClick={() => handleOpenAddOptionModal(attr.attribute_id, attr.attribute_name)}
              title={t('addOption') || 'Add Option'}
            />
          </Space.Compact>
        );

      case AttributeType.BOOLEAN:
        return (
          <Checkbox
            checked={attr.value === true || attr.value === 'true'}
            onChange={(e) => handleValueChange(index, e.target.checked)}
          />
        );

      case AttributeType.DATE:
        return (
          <DatePicker
            style={{ width: '100%' }}
            value={attr.value ? dayjs(attr.value as string) : null}
            onChange={(date) =>
              handleValueChange(index, date?.format('YYYY-MM-DD'))
            }
          />
        );

      case AttributeType.PRICE:
        return (
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            step={0.01}
            value={attr.value as number}
            onChange={(val) => handleValueChange(index, val)}
            prefix="$"
          />
        );

      case AttributeType.TEXTAREA:
        return (
          <Input.TextArea
            rows={3}
            value={attr.value as string}
            onChange={(e) => handleValueChange(index, e.target.value)}
            placeholder={t('enterValue') || 'Enter value'}
          />
        );

      case AttributeType.TEXT:
      default:
        return (
          <Input
            value={attr.value as string}
            onChange={(e) => handleValueChange(index, e.target.value)}
            placeholder={t('enterValue') || 'Enter value'}
          />
        );
    }
  };

  const columns = [
    {
      title: t('attribute') || 'Attribute',
      dataIndex: 'attribute_id',
      key: 'attribute',
      width: '30%',
      render: (_: any, record: SelectedProductAttribute, index: number) => (
        <Select
          style={{ width: '100%' }}
          value={record.attribute_id}
          onChange={(value) => handleAttributeChange(index, value)}
          placeholder={t('selectAttribute') || 'Select attribute'}
          options={availableAttributes
            .filter(
              (attr) =>
                !value.some(
                  (sel, i) =>
                    i !== index && sel.attribute_id === attr.attribute_id,
                ),
            )
            .map((attr) => ({
              label: `${attr.name} (${attr.type})`,
              value: attr.attribute_id,
            }))}
        />
      ),
    },
    {
      title: t('value') || 'Value',
      dataIndex: 'value',
      key: 'value',
      width: '60%',
      render: (_: any, record: SelectedProductAttribute, index: number) =>
        renderValueInput(record, index),
    },
    {
      title: t('action') || 'Action',
      key: 'action',
      width: '10%',
      render: (_: any, record: SelectedProductAttribute) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveAttribute(record.attribute_id)}
        />
      ),
    },
  ];

  return (
    <Card
      title={
        <Space className="w-full justify-between">
          <Text strong>{t('productAttributes.title') || 'Product Attributes'}</Text>
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={handleAddAttribute}
            loading={loading}
          >
            {t('addAttribute') || 'Add Attribute'}
          </Button>
        </Space>
      }
      size="small"
    >
      <Table
        columns={columns}
        dataSource={value}
        rowKey="attribute_id"
        pagination={false}
        loading={loading}
        locale={{
          emptyText:
            t('noAttributesAdded') ||
            'No attributes added. Click "Add Attribute" to start.',
        }}
      />

      <Modal
        title={`${t('addOptionFor') || 'Add option for'} "${modalAttributeName}"`}
        open={modalVisible}
        onOk={handleCreateOption}
        onCancel={handleCloseModal}
        confirmLoading={modalLoading}
        okText={t('add') || 'Add'}
        cancelText={t('cancel') || 'Cancel'}
      >
        <Form
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            label={t('optionValue') || 'Value'}
            name="value"
            rules={[{ required: true, message: t('form.pleaseEnterData') || 'Please enter value' }]}
            tooltip={t('optionValueTooltip') || 'Internal value used in code (e.g., "red", "xl")'}
          >
            <Input placeholder="e.g., red, xl, cotton" />
          </Form.Item>

          <Form.Item
            label={t('optionLabel') || 'Display Label'}
            name="label"
            rules={[{ required: true, message: t('form.pleaseEnterData') || 'Please enter label' }]}
            tooltip={t('optionLabelTooltip') || 'Label shown to users (e.g., "Red", "Extra Large")'}
          >
            <Input placeholder="e.g., Red, Extra Large, Cotton" />
          </Form.Item>

          <Form.Item
            label={t('displayOrder') || 'Display Order'}
            name="display_order"
            initialValue={99}
            tooltip={t('displayOrderTooltip') || 'Order in which option appears (lower numbers first)'}
          >
            <InputNumber min={0} max={999} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default ProductAttributes;
export type { SelectedProductAttribute };
