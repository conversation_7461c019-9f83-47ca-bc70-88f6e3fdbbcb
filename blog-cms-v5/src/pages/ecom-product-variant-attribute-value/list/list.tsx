import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { ListHeader } from '../../../components/list-header';
import { useGetPath, useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import { MODULE, MODULE_POPUP } from '../config';
import { ProductVariantAttributeValueModal } from '../form/modal';
import { useProductVariantAttributeValueStore } from '../store';
import { ProductVariantAttributeValueResponse } from '../type';
import { ProductVariantAttributeValueSearch } from './search';

interface ProductVariantAttributeValueListProps {
  variantId?: string;
}

function ProductVariantAttributeValueList({
  variantId,
}: ProductVariantAttributeValueListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const params = useParams();
  const routeVariantId = params.variantId;
  const effectiveVariantId = variantId || routeVariantId;
  const query = queryString.parse(search);
  const { loading } = useProductVariantAttributeValueStore();
  const getPath = useGetPath();

  const [cursor, setCursor] = useState<string>('');
  const [hasNext, setHasNext] = useState<boolean>(false);
  const [limit] = useState<number>(10);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<ProductVariantAttributeValueResponse[]>([]);
  const [idCurrent, setIdCurrent] = useState<number | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      try {
        const params = {
          ...query,
          ...filters,
          limit,
          cursor,
          ...payload,
        };

        // Add variant_id filter if provided
        if (effectiveVariantId) {
          params.variant_id = effectiveVariantId;
        }

        const cleanedParams = cleanParams(params);

        const response = await getItems(cleanedParams);
        if (response.status.success) {
          setItems(response.data || []);
          setHasNext(response.meta?.has_more || false);
          setCursor(response.meta?.next_cursor || '');
        } else {
          message.error(response.status.message);
        }
      } catch (error) {
        logger('Error fetching attribute values', error);
        message.error('Failed to fetch attribute values');
      }
    },
    [filters, query, cursor, limit, effectiveVariantId, logger],
  );

  useEffectOnce(() => {
    fetchData();
  });

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    setCursor(''); // Reset cursor when filters change
  };

  const handleDelete = async (id: number) => {
    const res = await deleteItem(String(id));
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.value_id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.value_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        if (effectiveVariantId) {
          navigate(
            getPath(`/${MODULE}/create?variant_id=${effectiveVariantId}`),
          );
        } else {
          navigate(`/${MODULE}/create`);
        }
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handleNextPage = () => {
    if (hasNext && cursor) {
      fetchData({ cursor });
    }
  };

  const handlePrevPage = () => {
    setCursor('');
    fetchData({ cursor: '' });
  };

  const renderValue = (record: ProductVariantAttributeValueResponse) => {
    if (record.value_text) return record.value_text;
    if (record.value_numeric !== undefined && record.value_numeric !== null)
      return record.value_numeric;
    if (record.value_date) return record.value_date;
    if (record.value_boolean !== undefined && record.value_boolean !== null)
      return record.value_boolean ? t('yes') : t('no');
    if (record.attribute_option) return record.attribute_option.value;
    return '-';
  };

  const columns = [
    {
      title: t('valueId'),
      dataIndex: 'value_id',
      key: 'value_id',
    },
    {
      title: t('variant'),
      dataIndex: 'variant',
      key: 'variant',
      render: (variant: any) => variant?.name || '-',
    },
    {
      title: t('attribute'),
      dataIndex: 'attribute',
      key: 'attribute',
      render: (attribute: any) =>
        attribute ? `${attribute.name} (${attribute.code})` : '-',
    },
    {
      title: t('value'),
      dataIndex: 'value',
      key: 'value',
      render: (_: any, record: ProductVariantAttributeValueResponse) =>
        renderValue(record),
    },
    {
      title: t('displayOrder'),
      dataIndex: 'display_order',
      key: 'display_order',
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.value_id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <ListHeader
        title={
          effectiveVariantId
            ? `${t('module')} - Variant ${effectiveVariantId}`
            : t('module')
        }
        module={MODULE}
        onAddClick={() => handleActions('add', null)}
        addButtonText={t('btnAdd')}
        showBackButton={true}
        backDestination="dashboard"
      />

      {!effectiveVariantId && (
        <div className="p-4 bg-white shadow">
          <ProductVariantAttributeValueSearch onSearch={handleFilters} />
        </div>
      )}

      <Space direction="vertical" className="bg-white w-full gap-0 mt-4">
        <Table
          columns={columns}
          dataSource={items}
          rowKey="value_id"
          loading={loading}
          pagination={false}
        />
        <div className="flex justify-end gap-2 p-4">
          <Button onClick={handlePrevPage} disabled={!cursor}>
            {t('btnPrevious')}
          </Button>
          <Button onClick={handleNextPage} disabled={!hasNext}>
            {t('btnNext')}
          </Button>
        </div>
      </Space>

      {showModal && (
        <ProductVariantAttributeValueModal
          open={showModal}
          id={idCurrent}
          variantId={effectiveVariantId}
          onClose={handleModal}
        />
      )}
    </div>
  );
}

export default ProductVariantAttributeValueList;
