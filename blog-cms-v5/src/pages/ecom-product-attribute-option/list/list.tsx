import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { ListHeader } from '../../../components/list-header';
import { useGetPath, useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getAttribute, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { ProductAttributeOptionModal } from '../form/modal';
import useProductAttributeOptionStore from '../store';
import { ProductAttributeOption } from '../type';
import { ProductAttributeOptionSearch } from './search';

interface ProductAttributeOptionListProps {
  attributeId?: string;
}

function ProductAttributeOptionList({
  attributeId,
}: ProductAttributeOptionListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const params = useParams();
  const routeAttributeId = params.attributeId;
  const effectiveAttributeId = attributeId || routeAttributeId;
  const query = queryString.parse(search);
  const { loading } = useProductAttributeOptionStore();
  const getPath = useGetPath();

  const [cursor, setCursor] = useState<string>('');
  const [hasNext, setHasNext] = useState<boolean>(false);
  const [limit] = useState<number>(10);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<ProductAttributeOption[]>([]);
  const [idCurrent, setIdCurrent] = useState<number | undefined>();
  const [attributeName, setAttributeName] = useState<string>('');

  const fetchAttributeData = useCallback(async () => {
    if (effectiveAttributeId) {
      try {
        const response = await getAttribute(effectiveAttributeId);
        if (response.status.success) {
          setAttributeName(response.data.name);
        }
      } catch (error) {
        logger('Error fetching attribute data', error);
      }
    }
  }, [effectiveAttributeId, logger]);

  const fetchData = useCallback(
    async (payload?: any) => {
      try {
        const params = {
          ...query,
          ...filters,
          limit,
          cursor,
          ...payload,
        };

        // Add attribute_id filter if provided
        if (effectiveAttributeId) {
          params.attribute_id = effectiveAttributeId;
        }

        const cleanedParams = cleanParams(params);

        const response = await getItems(cleanedParams);
        if (response.status.success) {
          setItems(response.data || []);
          setHasNext(response.meta?.has_next || false);
          setCursor(response.meta?.next_cursor || '');
        } else {
          message.error(response.status.message);
        }
      } catch (error) {
        logger('Error fetching options', error);
        message.error('Failed to fetch options');
      }
    },
    [filters, query, cursor, limit, effectiveAttributeId, logger],
  );

  useEffectOnce(() => {
    fetchData();
    if (effectiveAttributeId) {
      fetchAttributeData();
    }
  });

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    setCursor(''); // Reset cursor when filters change
  };

  const handleDelete = async (id: number) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.option_id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.option_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        if (effectiveAttributeId) {
          navigate(
            getPath(`/${MODULE}/create?attribute_id=${effectiveAttributeId}`),
          );
        } else {
          navigate(`/${MODULE}/create`);
        }
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handleNextPage = () => {
    if (hasNext && cursor) {
      fetchData({ cursor });
    }
  };

  const handlePrevPage = () => {
    setCursor('');
    fetchData({ cursor: '' });
  };

  const handleBackToAttributes = () => {
    navigate('/ecom-product-attribute');
  };

  const columns = [
    {
      title: t('value'),
      dataIndex: 'value',
      key: 'value',
    },
    {
      title: t('label'),
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: t('displayOrder'),
      dataIndex: 'display_order',
      key: 'display_order',
    },
    {
      title: t('isDefault'),
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault: boolean) =>
        isDefault ? (
          <Tag color="green">{t('yes')}</Tag>
        ) : (
          <Tag>{t('no')}</Tag>
        ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.option_id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <ListHeader
        title={
          effectiveAttributeId
            ? `${t('module')} - ${attributeName}`
            : t('module')
        }
        module={MODULE}
        onAddClick={() => handleActions('add', null)}
        addButtonText={t('btnAdd')}
        showBackButton={true}
        backDestination="dashboard"
        customActions={
          effectiveAttributeId ? (
            <Button onClick={handleBackToAttributes}>
              {t('btnBackToAttributes')}
            </Button>
          ) : undefined
        }
      />

      {!effectiveAttributeId && (
        <div className="p-4 bg-white shadow">
          <ProductAttributeOptionSearch onSearch={handleFilters} />
        </div>
      )}

      <Space direction="vertical" className="bg-white w-full gap-0 mt-4">
        <Table
          columns={columns}
          dataSource={items}
          rowKey="option_id"
          loading={loading}
          pagination={false}
        />
        <div className="flex justify-end gap-2 p-4">
          <Button onClick={handlePrevPage} disabled={!cursor}>
            {t('btnPrevious')}
          </Button>
          <Button onClick={handleNextPage} disabled={!hasNext}>
            {t('btnNext')}
          </Button>
        </div>
      </Space>

      {showModal && (
        <ProductAttributeOptionModal
          open={showModal}
          id={idCurrent}
          attributeId={effectiveAttributeId}
          onClose={handleModal}
        />
      )}
    </div>
  );
}

export default ProductAttributeOptionList;
