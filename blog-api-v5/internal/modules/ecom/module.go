package ecom

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"blog-api-v5/internal/modules/ecom/repositories/mysql"
	"blog-api-v5/internal/modules/ecom/services"
	"blog-api-v5/pkg/utils"
	"blog-api-v5/pkg/validator"
)

// RegisterRoutes registers all ecom module routes and dependencies
func RegisterRoutes(r *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Create repositories
	categoryRepo := mysql.NewEcomCategoryRepository(db)
	attributeGroupRepo := mysql.NewAttributeGroupRepository(db, logger)
	productAttributeRepo := mysql.NewProductAttributeRepository(db, logger)
	productAttributeOptionRepo := mysql.NewProductAttributeOptionRepositoryMySQL(db)
	productAttributeValueRepo := mysql.NewProductAttributeValueRepository(db)
	productRepo := mysql.NewProductRepository(db, logger)
	productVariantRepo := mysql.NewProductVariantRepository(db, logger)
	productVariantAttributeValueRepo := mysql.NewProductVariantAttributeValueRepository(db)
	productOptionGroupRepo := mysql.NewProductOptionGroupRepository(db, logger)
	productOptionValueRepo := mysql.NewProductOptionValueRepository(db, logger)
	productOptionsLinkRepo := mysql.NewProductOptionsLinkRepository(db)

	// Create services
	categoryService := services.NewEcomCategoryService(categoryRepo, logger)
	attributeGroupService := services.NewAttributeGroupService(attributeGroupRepo, logger)
	productAttributeService := services.NewProductAttributeService(productAttributeRepo, logger)
	productAttributeOptionService := services.NewProductAttributeOptionService(productAttributeOptionRepo)
	productAttributeValueService := services.NewProductAttributeValueService(productAttributeValueRepo)
	productService := services.NewProductService(productRepo, productAttributeValueRepo)
	productCategoryRepo := mysql.NewProductCategoryRepository(db)
	productCategoryService := services.NewProductCategoryService(productCategoryRepo)
	productVariantService := services.NewProductVariantService(productVariantRepo)
	productVariantAttributeValueService := services.NewProductVariantAttributeValueService(productVariantAttributeValueRepo)
	productOptionGroupService := services.NewProductOptionGroupService(productOptionGroupRepo, logger)
	productOptionValueService := services.NewProductOptionValueService(productOptionValueRepo, logger)
	productOptionsLinkService := services.NewProductOptionsLinkService(productOptionsLinkRepo)

	// Register ecom routes
	RegisterEcomRoutes(r, categoryService, attributeGroupService, productAttributeService, productAttributeOptionService, productAttributeValueService, productService, productCategoryService, productVariantService, productVariantAttributeValueService, productOptionGroupService, productOptionValueService, productOptionsLinkService, logger, db)
}
