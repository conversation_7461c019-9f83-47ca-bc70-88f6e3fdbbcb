package dto

import (
	"time"

	"blog-api-v5/internal/modules/ecom/models"
)

// ProductAttributeValueCreateRequest represents the request body for creating a product attribute value
type ProductAttributeValueCreateRequest struct {
	ReferenceID       uint                      `json:"reference_id" binding:"required"`
	ReferenceType     models.ReferenceType      `json:"reference_type" binding:"required,oneof=product variant"`
	AttributeID       uint                      `json:"attribute_id" binding:"required"`
	ValueText         *string                   `json:"value_text,omitempty"`
	ValueNumeric      *float64                  `json:"value_numeric,omitempty"`
	ValueDate         *time.Time                `json:"value_date,omitempty"`
	ValueBoolean      *bool                     `json:"value_boolean,omitempty"`
	AttributeOptionID *uint                     `json:"attribute_option_id,omitempty"`
}

// ProductAttributeValueUpdateRequest represents the request body for updating a product attribute value
type ProductAttributeValueUpdateRequest struct {
	ValueText         *string    `json:"value_text,omitempty"`
	ValueNumeric      *float64   `json:"value_numeric,omitempty"`
	ValueDate         *time.Time `json:"value_date,omitempty"`
	ValueBoolean      *bool      `json:"value_boolean,omitempty"`
	AttributeOptionID *uint      `json:"attribute_option_id,omitempty"`
}

// ProductAttributeValueResponse represents the response for a product attribute value
type ProductAttributeValueResponse struct {
	ValueID           uint                                `json:"value_id"`
	TenantID          uint                                `json:"tenant_id"`
	WebsiteID         uint                                `json:"website_id"`
	ReferenceID       uint                                `json:"reference_id"`
	ReferenceType     models.ReferenceType                `json:"reference_type"`
	AttributeID       uint                                `json:"attribute_id"`
	ValueText         *string                             `json:"value_text,omitempty"`
	ValueNumeric      *float64                            `json:"value_numeric,omitempty"`
	ValueDate         *time.Time                          `json:"value_date,omitempty"`
	ValueBoolean      *bool                               `json:"value_boolean,omitempty"`
	AttributeOptionID *uint                               `json:"attribute_option_id,omitempty"`
	CreatedAt         time.Time                           `json:"created_at"`
	UpdatedAt         time.Time                           `json:"updated_at"`
	Attribute         *ProductAttributeResponse           `json:"attribute,omitempty"`
	AttributeOption   *ProductAttributeOptionResponse     `json:"attribute_option,omitempty"`
}

// ProductAttributeValueBatchCreateRequest represents batch creation request
type ProductAttributeValueBatchCreateRequest struct {
	ReferenceID   uint                              `json:"reference_id" binding:"required"`
	ReferenceType models.ReferenceType              `json:"reference_type" binding:"required,oneof=product variant"`
	Values        []ProductAttributeValueCreateItem `json:"values" binding:"required,min=1,dive"`
}

// ProductAttributeValueCreateItem represents a single value in batch create
type ProductAttributeValueCreateItem struct {
	AttributeID       uint       `json:"attribute_id" binding:"required"`
	ValueText         *string    `json:"value_text,omitempty"`
	ValueNumeric      *float64   `json:"value_numeric,omitempty"`
	ValueDate         *time.Time `json:"value_date,omitempty"`
	ValueBoolean      *bool      `json:"value_boolean,omitempty"`
	AttributeOptionID *uint      `json:"attribute_option_id,omitempty"`
}

// ToProductAttributeValueResponse converts a ProductAttributeValue model to a response DTO
func ToProductAttributeValueResponse(value *models.ProductAttributeValue) *ProductAttributeValueResponse {
	if value == nil {
		return nil
	}

	response := &ProductAttributeValueResponse{
		ValueID:           value.ValueID,
		TenantID:          value.TenantID,
		WebsiteID:         value.WebsiteID,
		ReferenceID:       value.ReferenceID,
		ReferenceType:     value.ReferenceType,
		AttributeID:       value.AttributeID,
		ValueText:         value.ValueText,
		ValueNumeric:      value.ValueNumeric,
		ValueDate:         value.ValueDate,
		ValueBoolean:      value.ValueBoolean,
		AttributeOptionID: value.AttributeOptionID,
		CreatedAt:         value.CreatedAt,
		UpdatedAt:         value.UpdatedAt,
	}

	// Include relations if loaded
	if value.Attribute != nil {
		response.Attribute = ToProductAttributeResponse(value.Attribute)
	}
	if value.AttributeOption != nil {
		response.AttributeOption = ToProductAttributeOptionResponse(value.AttributeOption)
	}

	return response
}

// ToProductAttributeValueResponseList converts a slice of ProductAttributeValue models to response DTOs
func ToProductAttributeValueResponseList(values []models.ProductAttributeValue) []*ProductAttributeValueResponse {
	responses := make([]*ProductAttributeValueResponse, len(values))
	for i, value := range values {
		responses[i] = ToProductAttributeValueResponse(&value)
	}
	return responses
}

// ToProductAttributeValueResponseListFromPointers converts a slice of ProductAttributeValue model pointers to response DTOs
func ToProductAttributeValueResponseListFromPointers(values []*models.ProductAttributeValue) []*ProductAttributeValueResponse {
	responses := make([]*ProductAttributeValueResponse, len(values))
	for i, value := range values {
		responses[i] = ToProductAttributeValueResponse(value)
	}
	return responses
}
