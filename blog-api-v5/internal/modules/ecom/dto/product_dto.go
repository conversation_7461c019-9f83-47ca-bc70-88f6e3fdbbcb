package dto

import (
	"time"

	"blog-api-v5/internal/modules/ecom/models"
)

// ProductAttributeValueRequest represents attribute values for products
type ProductAttributeValueRequest struct {
	AttributeID       uint       `json:"attribute_id" binding:"required"`
	ValueText         *string    `json:"value_text,omitempty"`
	ValueNumeric      *float64   `json:"value_numeric,omitempty"`
	ValueDate         *time.Time `json:"value_date,omitempty"`
	ValueBoolean      *bool      `json:"value_boolean,omitempty"`
	AttributeOptionID *uint      `json:"attribute_option_id,omitempty"`
}

// ProductCreateRequest represents the request to create a new product
type ProductCreateRequest struct {
	CategoryID *uint `json:"category_id"`

	// Basic Information
	Name             string `json:"name" binding:"required,min=1,max=255"`
	Slug             string `json:"slug" binding:"required,min=1,max=255"`
	SKU              string `json:"sku" binding:"required,min=1,max=100"`
	Description      string `json:"description"`
	ShortDescription string `json:"short_description"`

	// Pricing
	Price     float64  `json:"price" binding:"required,gte=0"`
	SalePrice *float64 `json:"sale_price" binding:"omitempty,gte=0"`
	CostPrice *float64 `json:"cost_price" binding:"omitempty,gte=0"`

	// Inventory
	StockQuantity     int  `json:"stock_quantity" binding:"gte=0"`
	TrackInventory    bool `json:"track_inventory"`
	LowStockThreshold *int `json:"low_stock_threshold" binding:"omitempty,gte=0"`

	// Status & Visibility
	Status     models.ProductStatus `json:"status" binding:"required,oneof=draft active archived"`
	IsFeatured bool                 `json:"is_featured"`
	IsVisible  bool                 `json:"is_visible"`

	// Media
	FeaturedImage *string              `json:"featured_image"`
	GalleryImages models.GalleryImages `json:"gallery_images"`

	// SEO
	MetaTitle       *string `json:"meta_title" binding:"omitempty,max=255"`
	MetaDescription *string `json:"meta_description"`
	MetaKeywords    *string `json:"meta_keywords"`

	// Product Attributes
	ProductAttributeValues []ProductAttributeValueRequest `json:"product_attribute_values,omitempty"`
}

// ProductUpdateRequest represents the request to update a product
type ProductUpdateRequest struct {
	CategoryID *uint `json:"category_id"`

	// Basic Information
	Name             *string `json:"name" binding:"omitempty,min=1,max=255"`
	Slug             *string `json:"slug" binding:"omitempty,min=1,max=255"`
	SKU              *string `json:"sku" binding:"omitempty,min=1,max=100"`
	Description      *string `json:"description"`
	ShortDescription *string `json:"short_description"`

	// Pricing
	Price     *float64 `json:"price" binding:"omitempty,gte=0"`
	SalePrice *float64 `json:"sale_price" binding:"omitempty,gte=0"`
	CostPrice *float64 `json:"cost_price" binding:"omitempty,gte=0"`

	// Inventory
	StockQuantity     *int  `json:"stock_quantity" binding:"omitempty,gte=0"`
	TrackInventory    *bool `json:"track_inventory"`
	LowStockThreshold *int  `json:"low_stock_threshold" binding:"omitempty,gte=0"`

	// Status & Visibility
	Status     *models.ProductStatus `json:"status" binding:"omitempty,oneof=draft active archived"`
	IsFeatured *bool                 `json:"is_featured"`
	IsVisible  *bool                 `json:"is_visible"`

	// Media
	FeaturedImage *string              `json:"featured_image"`
	GalleryImages models.GalleryImages `json:"gallery_images"`

	// SEO
	MetaTitle       *string `json:"meta_title" binding:"omitempty,max=255"`
	MetaDescription *string `json:"meta_description"`
	MetaKeywords    *string `json:"meta_keywords"`

	// Product Attributes
	ProductAttributeValues []ProductAttributeValueRequest `json:"product_attribute_values,omitempty"`
}

// ProductResponse represents the response for a product
type ProductResponse struct {
	ProductID  uint  `json:"product_id"`
	TenantID   uint  `json:"tenant_id"`
	WebsiteID  uint  `json:"website_id"`
	CategoryID *uint `json:"category_id,omitempty"`

	// Basic Information
	Name             string `json:"name"`
	Slug             string `json:"slug"`
	SKU              string `json:"sku"`
	Description      string `json:"description,omitempty"`
	ShortDescription string `json:"short_description,omitempty"`

	// Pricing
	Price     float64  `json:"price"`
	SalePrice *float64 `json:"sale_price,omitempty"`
	CostPrice *float64 `json:"cost_price,omitempty"`

	// Inventory
	StockQuantity     int  `json:"stock_quantity"`
	TrackInventory    bool `json:"track_inventory"`
	LowStockThreshold *int `json:"low_stock_threshold,omitempty"`

	// Status & Visibility
	Status     models.ProductStatus `json:"status"`
	IsFeatured bool                 `json:"is_featured"`
	IsVisible  bool                 `json:"is_visible"`

	// Media
	FeaturedImage *string              `json:"featured_image,omitempty"`
	GalleryImages models.GalleryImages `json:"gallery_images,omitempty"`

	// SEO
	MetaTitle       *string `json:"meta_title,omitempty"`
	MetaDescription *string `json:"meta_description,omitempty"`
	MetaKeywords    *string `json:"meta_keywords,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relations
	Category               *EcomCategoryResponse            `json:"category,omitempty"`
	ProductAttributeValues []*ProductAttributeValueResponse `json:"product_attribute_values,omitempty"`
}

// ToProductResponse converts a Product model to ProductResponse
func ToProductResponse(product *models.Product) *ProductResponse {
	if product == nil {
		return nil
	}

	response := &ProductResponse{
		ProductID:         product.ProductID,
		TenantID:          product.TenantID,
		WebsiteID:         product.WebsiteID,
		CategoryID:        product.CategoryID,
		Name:              product.Name,
		Slug:              product.Slug,
		SKU:               product.SKU,
		Description:       product.Description,
		ShortDescription:  product.ShortDescription,
		Price:             product.Price,
		SalePrice:         product.SalePrice,
		CostPrice:         product.CostPrice,
		StockQuantity:     product.StockQuantity,
		TrackInventory:    product.TrackInventory,
		LowStockThreshold: product.LowStockThreshold,
		Status:            product.Status,
		IsFeatured:        product.IsFeatured,
		IsVisible:         product.IsVisible,
		FeaturedImage:     product.FeaturedImage,
		GalleryImages:     product.GalleryImages,
		MetaTitle:         product.MetaTitle,
		MetaDescription:   product.MetaDescription,
		MetaKeywords:      product.MetaKeywords,
		CreatedAt:         product.CreatedAt,
		UpdatedAt:         product.UpdatedAt,
	}

	if product.Category != nil {
		response.Category = ToEcomCategoryResponse(product.Category)
	}

	// Map product attribute values if loaded
	if len(product.ProductAttributeValues) > 0 {
		response.ProductAttributeValues = ToProductAttributeValueResponseListFromPointers(product.ProductAttributeValues)
	}

	return response
}

// ToProductResponseList converts a slice of Product models to ProductResponse slice
func ToProductResponseList(products []*models.Product) []*ProductResponse {
	responses := make([]*ProductResponse, len(products))
	for i, product := range products {
		responses[i] = ToProductResponse(product)
	}
	return responses
}
