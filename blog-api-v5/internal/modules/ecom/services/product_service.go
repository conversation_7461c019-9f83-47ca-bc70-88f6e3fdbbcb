package services

import (
	"context"
	"fmt"

	"blog-api-v5/internal/modules/ecom/dto"
	"blog-api-v5/internal/modules/ecom/models"
	"blog-api-v5/internal/modules/ecom/repositories"
	"blog-api-v5/pkg/pagination"
)

// ProductService handles business logic for products
type ProductService interface {
	List(ctx context.Context, req *pagination.CursorRequest, filters map[string]interface{}) ([]*dto.ProductResponse, *pagination.CursorResponse, error)
	GetByID(ctx context.Context, productID uint) (*dto.ProductResponse, error)
	GetBySKU(ctx context.Context, sku string) (*dto.ProductResponse, error)
	GetBySlug(ctx context.Context, slug string) (*dto.ProductResponse, error)
	Create(ctx context.Context, req *dto.ProductCreateRequest) (*dto.ProductResponse, error)
	Update(ctx context.Context, productID uint, req *dto.ProductUpdateRequest) (*dto.ProductResponse, error)
	Delete(ctx context.Context, productID uint) error
	UpdateStock(ctx context.Context, productID uint, quantity int) error
	BulkUpdateStatus(ctx context.Context, productIDs []uint, status models.ProductStatus) error
}

type productService struct {
	repo          repositories.ProductRepository
	attrValueRepo repositories.ProductAttributeValueRepository
}

// NewProductService creates a new product service
func NewProductService(repo repositories.ProductRepository, attrValueRepo repositories.ProductAttributeValueRepository) ProductService {
	return &productService{
		repo:          repo,
		attrValueRepo: attrValueRepo,
	}
}

func (s *productService) List(
	ctx context.Context, req *pagination.CursorRequest, filters map[string]interface{},
) ([]*dto.ProductResponse, *pagination.CursorResponse, error) {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return nil, nil, fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return nil, nil, fmt.Errorf("website_id not found in context")
	}

	// Include relations by default for better response data
	filters["include_relations"] = true

	products, cursorResp, err := s.repo.List(ctx, tenantID, websiteID, req, filters)
	if err != nil {
		return nil, nil, err
	}

	return dto.ToProductResponseList(products), cursorResp, nil
}

func (s *productService) GetByID(ctx context.Context, productID uint) (*dto.ProductResponse, error) {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return nil, fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return nil, fmt.Errorf("website_id not found in context")
	}

	product, err := s.repo.GetByID(ctx, tenantID, websiteID, productID)
	if err != nil {
		return nil, err
	}

	return dto.ToProductResponse(product), nil
}

func (s *productService) GetBySKU(ctx context.Context, sku string) (*dto.ProductResponse, error) {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return nil, fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return nil, fmt.Errorf("website_id not found in context")
	}

	product, err := s.repo.GetBySKU(ctx, tenantID, websiteID, sku)
	if err != nil {
		return nil, err
	}

	return dto.ToProductResponse(product), nil
}

func (s *productService) GetBySlug(ctx context.Context, slug string) (*dto.ProductResponse, error) {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return nil, fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return nil, fmt.Errorf("website_id not found in context")
	}

	product, err := s.repo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		return nil, err
	}

	return dto.ToProductResponse(product), nil
}

func (s *productService) Create(
	ctx context.Context, req *dto.ProductCreateRequest,
) (*dto.ProductResponse, error) {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return nil, fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return nil, fmt.Errorf("website_id not found in context")
	}

	// Check if SKU already exists
	existingProduct, err := s.repo.GetBySKU(ctx, tenantID, websiteID, req.SKU)
	if err == nil && existingProduct != nil {
		return nil, fmt.Errorf("product with SKU '%s' already exists", req.SKU)
	}

	// Check if slug already exists
	existingProduct, err = s.repo.GetBySlug(ctx, tenantID, websiteID, req.Slug)
	if err == nil && existingProduct != nil {
		return nil, fmt.Errorf("product with slug '%s' already exists", req.Slug)
	}

	product := &models.Product{
		TenantID:          tenantID,
		WebsiteID:         websiteID,
		CategoryID:        req.CategoryID,
		Name:              req.Name,
		Slug:              req.Slug,
		SKU:               req.SKU,
		Description:       req.Description,
		ShortDescription:  req.ShortDescription,
		Price:             req.Price,
		SalePrice:         req.SalePrice,
		CostPrice:         req.CostPrice,
		StockQuantity:     req.StockQuantity,
		TrackInventory:    req.TrackInventory,
		LowStockThreshold: req.LowStockThreshold,
		Status:            req.Status,
		IsFeatured:        req.IsFeatured,
		IsVisible:         req.IsVisible,
		FeaturedImage:     req.FeaturedImage,
		GalleryImages:     req.GalleryImages,
		MetaTitle:         req.MetaTitle,
		MetaDescription:   req.MetaDescription,
		MetaKeywords:      req.MetaKeywords,
	}

	if err := s.repo.Create(ctx, product); err != nil {
		return nil, err
	}

	// Handle product attribute values if provided
	if len(req.ProductAttributeValues) > 0 {
		values := make([]models.ProductAttributeValue, len(req.ProductAttributeValues))
		for i, item := range req.ProductAttributeValues {
			values[i] = models.ProductAttributeValue{
				TenantID:          tenantID,
				WebsiteID:         websiteID,
				ReferenceID:       product.ProductID,
				ReferenceType:     models.ReferenceTypeProduct,
				AttributeID:       item.AttributeID,
				ValueText:         item.ValueText,
				ValueNumeric:      item.ValueNumeric,
				ValueDate:         item.ValueDate,
				ValueBoolean:      item.ValueBoolean,
				AttributeOptionID: item.AttributeOptionID,
			}
		}

		if err := s.attrValueRepo.BatchCreate(ctx, values); err != nil {
			return nil, fmt.Errorf("failed to create attribute values: %w", err)
		}
	}

	// Fetch the created product with relations
	createdProduct, err := s.repo.GetByID(ctx, tenantID, websiteID, product.ProductID)
	if err != nil {
		return nil, err
	}

	return dto.ToProductResponse(createdProduct), nil
}

func (s *productService) Update(
	ctx context.Context, productID uint, req *dto.ProductUpdateRequest,
) (*dto.ProductResponse, error) {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return nil, fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return nil, fmt.Errorf("website_id not found in context")
	}

	product, err := s.repo.GetByID(ctx, tenantID, websiteID, productID)
	if err != nil {
		return nil, err
	}

	// Check if new SKU already exists (if SKU is being updated)
	if req.SKU != nil && *req.SKU != product.SKU {
		existingProduct, err := s.repo.GetBySKU(ctx, tenantID, websiteID, *req.SKU)
		if err == nil && existingProduct != nil && existingProduct.ProductID != productID {
			return nil, fmt.Errorf("product with SKU '%s' already exists", *req.SKU)
		}
	}

	// Check if new slug already exists (if slug is being updated)
	if req.Slug != nil && *req.Slug != product.Slug {
		existingProduct, err := s.repo.GetBySlug(ctx, tenantID, websiteID, *req.Slug)
		if err == nil && existingProduct != nil && existingProduct.ProductID != productID {
			return nil, fmt.Errorf("product with slug '%s' already exists", *req.Slug)
		}
	}

	// Update fields if provided
	if req.CategoryID != nil {
		product.CategoryID = req.CategoryID
	}
	if req.Name != nil {
		product.Name = *req.Name
	}
	if req.Slug != nil {
		product.Slug = *req.Slug
	}
	if req.SKU != nil {
		product.SKU = *req.SKU
	}
	if req.Description != nil {
		product.Description = *req.Description
	}
	if req.ShortDescription != nil {
		product.ShortDescription = *req.ShortDescription
	}
	if req.Price != nil {
		product.Price = *req.Price
	}
	if req.SalePrice != nil {
		product.SalePrice = req.SalePrice
	}
	if req.CostPrice != nil {
		product.CostPrice = req.CostPrice
	}
	if req.StockQuantity != nil {
		product.StockQuantity = *req.StockQuantity
	}
	if req.TrackInventory != nil {
		product.TrackInventory = *req.TrackInventory
	}
	if req.LowStockThreshold != nil {
		product.LowStockThreshold = req.LowStockThreshold
	}
	if req.Status != nil {
		product.Status = *req.Status
	}
	if req.IsFeatured != nil {
		product.IsFeatured = *req.IsFeatured
	}
	if req.IsVisible != nil {
		product.IsVisible = *req.IsVisible
	}
	if req.FeaturedImage != nil {
		product.FeaturedImage = req.FeaturedImage
	}
	if req.GalleryImages != nil {
		product.GalleryImages = req.GalleryImages
	}
	if req.MetaTitle != nil {
		product.MetaTitle = req.MetaTitle
	}
	if req.MetaDescription != nil {
		product.MetaDescription = req.MetaDescription
	}
	if req.MetaKeywords != nil {
		product.MetaKeywords = req.MetaKeywords
	}

	if err := s.repo.Update(ctx, product); err != nil {
		return nil, err
	}

	// Handle product attribute values if provided
	if req.ProductAttributeValues != nil {
		// Delete existing attribute values
		if err := s.attrValueRepo.DeleteByReferenceID(ctx, tenantID, websiteID, models.ReferenceTypeProduct, productID); err != nil {
			return nil, fmt.Errorf("failed to delete existing attribute values: %w", err)
		}

		// Create new attribute values
		if len(req.ProductAttributeValues) > 0 {
			values := make([]models.ProductAttributeValue, len(req.ProductAttributeValues))
			for i, item := range req.ProductAttributeValues {
				values[i] = models.ProductAttributeValue{
					TenantID:          tenantID,
					WebsiteID:         websiteID,
					ReferenceID:       productID,
					ReferenceType:     models.ReferenceTypeProduct,
					AttributeID:       item.AttributeID,
					ValueText:         item.ValueText,
					ValueNumeric:      item.ValueNumeric,
					ValueDate:         item.ValueDate,
					ValueBoolean:      item.ValueBoolean,
					AttributeOptionID: item.AttributeOptionID,
				}
			}

			if err := s.attrValueRepo.BatchCreate(ctx, values); err != nil {
				return nil, fmt.Errorf("failed to create attribute values: %w", err)
			}
		}
	}

	// Fetch updated product with relations
	updatedProduct, err := s.repo.GetByID(ctx, tenantID, websiteID, product.ProductID)
	if err != nil {
		return nil, err
	}

	return dto.ToProductResponse(updatedProduct), nil
}

func (s *productService) Delete(ctx context.Context, productID uint) error {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return fmt.Errorf("website_id not found in context")
	}

	return s.repo.Delete(ctx, tenantID, websiteID, productID)
}

func (s *productService) UpdateStock(ctx context.Context, productID uint, quantity int) error {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return fmt.Errorf("website_id not found in context")
	}

	return s.repo.UpdateStock(ctx, tenantID, websiteID, productID, quantity)
}

func (s *productService) BulkUpdateStatus(
	ctx context.Context, productIDs []uint, status models.ProductStatus,
) error {
	tenantID, ok := ctx.Value("tenant_id").(uint)
	if !ok {
		return fmt.Errorf("tenant_id not found in context")
	}

	websiteID, ok := ctx.Value("website_id").(uint)
	if !ok {
		return fmt.Errorf("website_id not found in context")
	}

	return s.repo.BulkUpdateStatus(ctx, tenantID, websiteID, productIDs, status)
}
