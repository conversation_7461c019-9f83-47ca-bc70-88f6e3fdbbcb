package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"blog-api-v5/internal/modules/ecom/models"
	"blog-api-v5/internal/modules/ecom/repositories"
	"blog-api-v5/pkg/pagination"
	"blog-api-v5/pkg/utils"
)

type productRepositoryMySQL struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewProductRepository creates a new product repository
func NewProductRepository(db *gorm.DB, logger utils.Logger) repositories.ProductRepository {
	return &productRepositoryMySQL{
		db:     db,
		logger: logger,
	}
}

// List retrieves products with cursor pagination and filters
func (r *productRepositoryMySQL) List(
	ctx context.Context, tenantID, websiteID uint,
	req *pagination.CursorRequest, filters map[string]interface{},
) ([]*models.Product, *pagination.CursorResponse, error) {
	var products []*models.Product

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID)

	// Apply filters
	if categoryID, ok := filters["category_id"].(uint); ok && categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	if status, ok := filters["status"].(models.ProductStatus); ok && status != "" {
		query = query.Where("status = ?", status)
	}

	if isFeatured, ok := filters["is_featured"].(bool); ok {
		query = query.Where("is_featured = ?", isFeatured)
	}

	if isVisible, ok := filters["is_visible"].(bool); ok {
		query = query.Where("is_visible = ?", isVisible)
	}

	if search, ok := filters["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where(
			"name LIKE ? OR sku LIKE ? OR description LIKE ?",
			searchPattern, searchPattern, searchPattern,
		)
	}

	// Cursor pagination
	if req.Cursor != "" {
		query = query.Where("product_id > ?", req.Cursor)
	}

	// Order and limit (fetch one extra to determine if there's a next page)
	query = query.Order("product_id ASC").Limit(req.Limit + 1)

	// Preload relations if requested
	if includeRelations, ok := filters["include_relations"].(bool); ok && includeRelations {
		query = query.Preload("Category")
		query = query.Preload("ProductAttributeValues")
		query = query.Preload("ProductAttributeValues.Attribute")
		query = query.Preload("ProductAttributeValues.AttributeOption")
	}

	if err := query.Find(&products).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list products")
		return nil, nil, err
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(products) > req.Limit,
		Limit:   req.Limit,
	}

	// If we have more items than requested, trim the list and set next cursor
	if response.HasNext && len(products) > 0 {
		products = products[:req.Limit]
		response.NextCursor = fmt.Sprintf("%d", products[len(products)-1].ProductID)
	}

	return products, response, nil
}

// GetByID retrieves a product by its ID
func (r *productRepositoryMySQL) GetByID(
	ctx context.Context, tenantID, websiteID, productID uint,
) (*models.Product, error) {
	var product models.Product

	if err := r.db.WithContext(ctx).
		Where("product_id = ? AND tenant_id = ? AND website_id = ?", productID, tenantID, websiteID).
		Preload("Category").
		Preload("ProductAttributeValues").
		Preload("ProductAttributeValues.Attribute").
		Preload("ProductAttributeValues.AttributeOption").
		First(&product).Error; err != nil {
		r.logger.WithError(err).Errorf("Failed to get product with ID %d", productID)
		return nil, err
	}

	return &product, nil
}

// GetBySKU retrieves a product by its SKU
func (r *productRepositoryMySQL) GetBySKU(
	ctx context.Context, tenantID, websiteID uint, sku string,
) (*models.Product, error) {
	var product models.Product

	if err := r.db.WithContext(ctx).
		Where("sku = ? AND tenant_id = ? AND website_id = ?", sku, tenantID, websiteID).
		Preload("Category").
		Preload("ProductAttributeValues").
		Preload("ProductAttributeValues.Attribute").
		Preload("ProductAttributeValues.AttributeOption").
		First(&product).Error; err != nil {
		return nil, err
	}

	return &product, nil
}

// GetBySlug retrieves a product by its slug
func (r *productRepositoryMySQL) GetBySlug(
	ctx context.Context, tenantID, websiteID uint, slug string,
) (*models.Product, error) {
	var product models.Product

	if err := r.db.WithContext(ctx).
		Where("slug = ? AND tenant_id = ? AND website_id = ?", slug, tenantID, websiteID).
		Preload("Category").
		Preload("ProductAttributeValues").
		Preload("ProductAttributeValues.Attribute").
		Preload("ProductAttributeValues.AttributeOption").
		First(&product).Error; err != nil {
		return nil, err
	}

	return &product, nil
}

// Create creates a new product
func (r *productRepositoryMySQL) Create(ctx context.Context, product *models.Product) error {
	if err := r.db.WithContext(ctx).Create(product).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create product")
		return err
	}

	r.logger.Infof("Product created with ID: %d", product.ProductID)
	return nil
}

// Update updates an existing product
func (r *productRepositoryMySQL) Update(ctx context.Context, product *models.Product) error {
	if err := r.db.WithContext(ctx).Save(product).Error; err != nil {
		r.logger.WithError(err).Errorf("Failed to update product with ID %d", product.ProductID)
		return err
	}

	r.logger.Infof("Product updated with ID: %d", product.ProductID)
	return nil
}

// Delete deletes a product
func (r *productRepositoryMySQL) Delete(ctx context.Context, tenantID, websiteID, productID uint) error {
	result := r.db.WithContext(ctx).
		Where("product_id = ? AND tenant_id = ? AND website_id = ?", productID, tenantID, websiteID).
		Delete(&models.Product{})

	if result.Error != nil {
		r.logger.WithError(result.Error).Errorf("Failed to delete product with ID %d", productID)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	r.logger.Infof("Product deleted with ID: %d", productID)
	return nil
}

// UpdateStock updates product stock quantity
func (r *productRepositoryMySQL) UpdateStock(
	ctx context.Context, tenantID, websiteID, productID uint, quantity int,
) error {
	result := r.db.WithContext(ctx).
		Model(&models.Product{}).
		Where("product_id = ? AND tenant_id = ? AND website_id = ?", productID, tenantID, websiteID).
		Update("stock_quantity", quantity)

	if result.Error != nil {
		r.logger.WithError(result.Error).Errorf("Failed to update stock for product ID %d", productID)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	r.logger.Infof("Stock updated for product ID: %d to quantity: %d", productID, quantity)
	return nil
}

// BulkUpdateStatus updates status for multiple products
func (r *productRepositoryMySQL) BulkUpdateStatus(
	ctx context.Context, tenantID, websiteID uint, productIDs []uint, status models.ProductStatus,
) error {
	if len(productIDs) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).
		Model(&models.Product{}).
		Where("product_id IN ? AND tenant_id = ? AND website_id = ?", productIDs, tenantID, websiteID).
		Update("status", status)

	if result.Error != nil {
		r.logger.WithError(result.Error).Error("Failed to bulk update product status")
		return result.Error
	}

	r.logger.Infof("Bulk updated %d products to status: %s", result.RowsAffected, status)
	return nil
}
