package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// ProductStatus represents the status of a product
type ProductStatus string

const (
	ProductStatusDraft    ProductStatus = "draft"
	ProductStatusActive   ProductStatus = "active"
	ProductStatusArchived ProductStatus = "archived"
)

// GalleryImages represents an array of image URLs stored as JSON
type GalleryImages []string

// <PERSON>an implements the sql.Scanner interface for GalleryImages
func (g *GalleryImages) Scan(value interface{}) error {
	if value == nil {
		*g = GalleryImages{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal GalleryImages value: %v", value)
	}

	result := GalleryImages{}
	if err := json.Unmarshal(bytes, &result); err != nil {
		return err
	}

	*g = result
	return nil
}

// Value implements the driver.Valuer interface for GalleryImages
func (g GalleryImages) Value() (driver.Value, error) {
	if len(g) == 0 {
		return nil, nil
	}
	return json.Marshal(g)
}

// Product represents an ecommerce product
type Product struct {
	ProductID     uint          `gorm:"primaryKey;autoIncrement;column:product_id" json:"product_id"`
	TenantID      uint          `gorm:"column:tenant_id;not null;index" json:"tenant_id"`
	WebsiteID     uint          `gorm:"column:website_id;not null;index" json:"website_id"`
	CategoryID    *uint         `gorm:"column:category_id;index" json:"category_id,omitempty"`

	// Basic Information
	Name             string `gorm:"column:name;size:255;not null" json:"name"`
	Slug             string `gorm:"column:slug;size:255;not null;index" json:"slug"`
	SKU              string `gorm:"column:sku;size:100;not null;uniqueIndex:uk_tenant_website_sku" json:"sku"`
	Description      string `gorm:"column:description;type:longtext" json:"description,omitempty"`
	ShortDescription string `gorm:"column:short_description;type:text" json:"short_description,omitempty"`

	// Pricing
	Price     float64  `gorm:"column:price;type:decimal(20,2);not null;default:0" json:"price"`
	SalePrice *float64 `gorm:"column:sale_price;type:decimal(20,2)" json:"sale_price,omitempty"`
	CostPrice *float64 `gorm:"column:cost_price;type:decimal(20,2)" json:"cost_price,omitempty"`

	// Inventory
	StockQuantity     int   `gorm:"column:stock_quantity;not null;default:0" json:"stock_quantity"`
	TrackInventory    bool  `gorm:"column:track_inventory;not null;default:true" json:"track_inventory"`
	LowStockThreshold *int  `gorm:"column:low_stock_threshold" json:"low_stock_threshold,omitempty"`

	// Status & Visibility
	Status     ProductStatus `gorm:"column:status;type:enum('draft','active','archived');not null;default:'draft';index" json:"status"`
	IsFeatured bool          `gorm:"column:is_featured;not null;default:false;index" json:"is_featured"`
	IsVisible  bool          `gorm:"column:is_visible;not null;default:true;index" json:"is_visible"`

	// Media
	FeaturedImage *string        `gorm:"column:featured_image;size:500" json:"featured_image,omitempty"`
	GalleryImages GalleryImages  `gorm:"column:gallery_images;type:json" json:"gallery_images,omitempty"`

	// SEO
	MetaTitle       *string `gorm:"column:meta_title;size:255" json:"meta_title,omitempty"`
	MetaDescription *string `gorm:"column:meta_description;type:text" json:"meta_description,omitempty"`
	MetaKeywords    *string `gorm:"column:meta_keywords;type:text" json:"meta_keywords,omitempty"`

	// Timestamps
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`

	// Relations
	Category               *EcomCategory               `gorm:"foreignKey:CategoryID;references:ID" json:"category,omitempty"`
	ProductAttributeValues []*ProductAttributeValue    `gorm:"foreignKey:ReferenceID;references:ProductID;constraint:OnDelete:CASCADE" json:"product_attribute_values,omitempty"`
}

// TableName specifies the table name for the Product model
func (Product) TableName() string {
	return "ecom_products"
}
